const mysql = require('mysql2/promise');
const readline = require('readline');
require('dotenv').config();

async function askConfirmation(message) {
    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
    });

    return new Promise((resolve) => {
        rl.question(message, (answer) => {
            rl.close();
            resolve(answer.toLowerCase() === 'y');
        });
    });
}

async function getTableColumns(connection, tableName, databaseName) {
    const [columns] = await connection.query(`
        SELECT 
            COLUMN_NAME as name,
            DATA_TYPE as type,
            IS_NULLABLE as nullable,
            COLUMN_DEFAULT as defaultValue,
            CHARACTER_MAXIMUM_LENGTH as maxLength,
            \`NUMERIC_PRECISION\` as \`precision\`,
            \`NUMERIC_SCALE\` as scale
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ? 
        ORDER BY ORDINAL_POSITION
    `, [databaseName, tableName]);
    
    return columns;
}

function getDefaultValueForColumn(column) {
    const { type, nullable, defaultValue } = column;
    
    // If column has explicit default, use it
    if (defaultValue !== null) {
        return defaultValue;
    }
    
    // If nullable, use NULL
    if (nullable === 'YES') {
        return null;
    }
    
    // Generate sensible defaults based on data type
    switch (type.toLowerCase()) {
        case 'tinyint':
        case 'smallint':
        case 'mediumint':
        case 'int':
        case 'integer':
        case 'bigint':
        case 'decimal':
        case 'numeric':
        case 'float':
        case 'double':
        case 'bit':
            return 0;
        case 'char':
        case 'varchar':
        case 'text':
        case 'tinytext':
        case 'mediumtext':
        case 'longtext':
        case 'binary':
        case 'varbinary':
        case 'blob':
        case 'tinyblob':
        case 'mediumblob':
        case 'longblob':
            return '';
        case 'date':
            return '1970-01-01';
        case 'datetime':
        case 'timestamp':
            return '1970-01-01 00:00:00';
        case 'time':
            return '00:00:00';
        case 'year':
            return '1970';
        case 'json':
            return '{}';
        case 'boolean':
        case 'bool':
            return false;
        default:
            return null;
    }
}

async function transferDatabase() {
    const remoteConfig = {
        host: process.env.REMOTE_DB_HOST,
        user: process.env.REMOTE_DB_USER,
        password: process.env.REMOTE_DB_PASSWORD,
        database: process.env.REMOTE_DB_NAME,
        port: process.env.REMOTE_DB_PORT
    };

    const localConfig = {
        host: process.env.LOCAL_DB_HOST,
        user: process.env.LOCAL_DB_USER,
        password: process.env.LOCAL_DB_PASSWORD,
        database: process.env.LOCAL_DB_NAME,
        port: process.env.LOCAL_DB_PORT
    };

    // Log connection details (without passwords)
    console.log('📋 Connection Details:');
    console.log(`  Remote: ${remoteConfig.user}@${remoteConfig.host}:${remoteConfig.port}/${remoteConfig.database}`);
    console.log(`  Local:  ${localConfig.user}@${localConfig.host}:${localConfig.port}/${localConfig.database}`);
    console.log('');

    // Add confirmation prompt
    const confirmed = await askConfirmation(
        `Data will be copied from ${remoteConfig.host}::${remoteConfig.database} to ${localConfig.host}::${localConfig.database}.\nExisting data will be cleared but table structure preserved. Continue? (y/n): `
    );

    if (!confirmed) {
        console.log('❌ Transfer cancelled.');
        process.exit(0);
    }

    let remoteConnection, localConnection;

    try {
        console.log('🔌 Connecting to databases...');
        
        // Create connections
        remoteConnection = await mysql.createConnection(remoteConfig);
        console.log('✅ Connected to remote database');
        
        localConnection = await mysql.createConnection(localConfig);
        console.log('✅ Connected to local database');

        // Disable foreign key checks
        await localConnection.query('SET FOREIGN_KEY_CHECKS = 0');
        console.log('🔧 Disabled foreign key checks for local database');

        // Get list of all tables from remote database
        const [remoteTables] = await remoteConnection.query('SHOW TABLES');
        const tableNames = remoteTables.map(row => row[`Tables_in_${remoteConfig.database}`]);

        console.log(`\n📊 Found ${tableNames.length} tables to transfer:`);
        tableNames.forEach((name, index) => {
            console.log(`  ${index + 1}. ${name}`);
        });
        console.log('');

        let totalRowsTransferred = 0;
        let tablesProcessed = 0;

        for (const tableName of tableNames) {
            console.log(`\n🔄 Processing table: ${tableName}`);

            // Check if table exists in local database
            const [localTableExists] = await localConnection.query(
                `SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = ? AND table_name = ?`,
                [localConfig.database, tableName]
            );

            let tableCreated = false;
            if (localTableExists[0].count === 0) {
                console.log(`⚠️  Table ${tableName} does not exist in local database - creating it`);

                try {
                    // Get table structure from remote database
                    const [createTableRows] = await remoteConnection.query(`SHOW CREATE TABLE \`${tableName}\``);

                    // Debug: Check what we got back
                    console.log(`  🔍 Debug: createTableRows length: ${createTableRows.length}`);
                    if (createTableRows.length > 0) {
                        console.log(`  🔍 Debug: First row keys: ${Object.keys(createTableRows[0]).join(', ')}`);
                    }

                    // Try different possible property names for the CREATE TABLE statement
                    let createTableSql;
                    if (createTableRows[0]['Create Table']) {
                        createTableSql = createTableRows[0]['Create Table'];
                    } else if (createTableRows[0]['Create table']) {
                        createTableSql = createTableRows[0]['Create table'];
                    } else if (createTableRows[0].createTable) {
                        createTableSql = createTableRows[0].createTable;
                    } else {
                        // If none of the expected properties exist, show what we have
                        console.error(`  ❌ Could not find CREATE TABLE statement in result:`, createTableRows[0]);
                        throw new Error(`Could not extract CREATE TABLE statement for table ${tableName}`);
                    }

                    console.log(`  🔍 Debug: CREATE TABLE SQL length: ${createTableSql ? createTableSql.length : 'undefined'}`);

                    if (!createTableSql) {
                        throw new Error(`CREATE TABLE statement is empty for table ${tableName}`);
                    }

                    // Create table in local database
                    await localConnection.query(createTableSql);
                    console.log(`  ✅ Created table structure in local database`);
                    tableCreated = true;
                } catch (createError) {
                    console.error(`  ❌ Error creating table ${tableName}:`, createError.message);
                    throw createError;
                }
            }

            // Get column information for both databases
            const remoteColumns = await getTableColumns(remoteConnection, tableName, remoteConfig.database);
            const localColumns = await getTableColumns(localConnection, tableName, localConfig.database);

            console.log(`  📝 Remote columns: ${remoteColumns.length}, Local columns: ${localColumns.length}`);

            // Create maps for easier lookup
            const remoteColumnMap = new Map(remoteColumns.map(col => [col.name, col]));
            const localColumnMap = new Map(localColumns.map(col => [col.name, col]));

            // Find columns that exist in local but not in remote
            const extraLocalColumns = localColumns.filter(col => !remoteColumnMap.has(col.name));
            if (extraLocalColumns.length > 0) {
                console.log(`  🆕 Extra columns in local (will use defaults): ${extraLocalColumns.map(col => col.name).join(', ')}`);
            }

            // Find columns that exist in remote but not in local
            const missingLocalColumns = remoteColumns.filter(col => !localColumnMap.has(col.name));
            if (missingLocalColumns.length > 0) {
                console.log(`  ⚠️  Columns in remote but not in local (will be ignored): ${missingLocalColumns.map(col => col.name).join(', ')}`);
            }

            // Skip specific tables if needed
            if (['failed_jobs'].includes(tableName)) {
                console.log(`  ⏭️  Skipping table: ${tableName}`);
                continue;
            }

            // Clear existing data from local table (only if table wasn't just created)
            if (!tableCreated) {
                const [deleteResult] = await localConnection.query(`DELETE FROM \`${tableName}\``);
                console.log(`  🧹 Cleared ${deleteResult.affectedRows} existing rows`);
            } else {
                console.log(`  🆕 Table was just created, no existing data to clear`);
            }

            // Get all data from remote table
            const [rows] = await remoteConnection.query(`SELECT * FROM \`${tableName}\``);

            if (rows.length === 0) {
                console.log(`  📭 No data to transfer`);
                tablesProcessed++;
                continue;
            }

            // Prepare column mappings for insert - only use columns that exist in local table
            // and make sure we have unique column names
            const uniqueLocalColumns = [...new Set(localColumns.map(col => col.name))];
            
            // Filter to only include columns that actually exist in the local table structure
            const insertColumns = uniqueLocalColumns.filter(colName => 
                localColumnMap.has(colName)
            );
            const escapedColumns = insertColumns.map(col => `\`${col}\``);

            console.log(`  🔧 Preparing to insert into ${insertColumns.length} unique local columns`);

            // Insert data in batches
            const batchSize = 1000;
            let rowsInserted = 0;

            for (let i = 0; i < rows.length; i += batchSize) {
                const batch = rows.slice(i, i + batchSize);
                
                // Build values for each row, handling missing columns
                const values = [];
                batch.forEach(remoteRow => {
                    insertColumns.forEach(localColName => {
                        if (remoteRow.hasOwnProperty(localColName)) {
                            // Column exists in remote data
                            values.push(remoteRow[localColName]);
                        } else {
                            // Column doesn't exist in remote, use default
                            const localColumn = localColumnMap.get(localColName);
                            const defaultValue = getDefaultValueForColumn(localColumn);
                            values.push(defaultValue);
                        }
                    });
                });
                
                // Build placeholders
                const valuesPlaceholders = batch.map(() => `(${insertColumns.map(() => '?').join(',')})`).join(',');
                
                // Create the batch insert query
                const batchInsertSql = `INSERT INTO \`${tableName}\` (${escapedColumns.join(',')}) VALUES ${valuesPlaceholders}`;
                
                await localConnection.query(batchInsertSql, values);
                rowsInserted += batch.length;
                
                const progress = Math.min(i + batchSize, rows.length);
                console.log(`  📥 Inserted ${progress}/${rows.length} rows`);
            }

            console.log(`  ✅ Successfully transferred ${rowsInserted} rows for table: ${tableName}`);
            totalRowsTransferred += rowsInserted;
            tablesProcessed++;
        }

        // Re-enable foreign key checks
        await localConnection.query('SET FOREIGN_KEY_CHECKS = 1');
        console.log('\n🔧 Re-enabled foreign key checks');

        console.log('\n🎉 Database transfer completed successfully!');
        console.log(`📊 Summary:`);
        console.log(`  - Tables processed: ${tablesProcessed}/${tableNames.length}`);
        console.log(`  - Total rows transferred: ${totalRowsTransferred.toLocaleString()}`);

    } catch (error) {
        console.error('\n❌ Error during database transfer:', error);
        console.error('\n🔍 Connection details check:');
        console.error(`  - Verify environment variables in .env file`);
        console.error(`  - Ensure both database servers are running`);
        console.error(`  - Check network connectivity and firewall settings`);
        console.error(`  - Verify database credentials and permissions`);
    } finally {
        // Close connections
        if (remoteConnection) {
            await remoteConnection.end();
            console.log('🔌 Remote database connection closed');
        }
        if (localConnection) {
            await localConnection.end();
            console.log('🔌 Local database connection closed');
        }
    }
}

// Execute the transfer
transferDatabase();
