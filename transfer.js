const mysql = require('mysql2/promise');
const readline = require('readline');
require('dotenv').config();

async function askConfirmation(message) {
    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
    });

    return new Promise((resolve) => {
        rl.question(message, (answer) => {
            rl.close();
            resolve(answer.toLowerCase() === 'y');
        });
    });
}

// Function to estimate the size of a data packet in bytes
function estimatePacketSize(tableName, columns, batch) {
    let size = 0;

    // Base query overhead
    const baseQuery = `INSERT INTO \`${tableName}\` (${columns.join(',')}) VALUES `;
    size += baseQuery.length;

    // Estimate size for each row
    for (const row of batch) {
        size += 2; // parentheses
        for (let i = 0; i < Object.values(row).length; i++) {
            const value = Object.values(row)[i];
            if (value === null) {
                size += 4; // 'NULL'
            } else if (typeof value === 'string') {
                size += value.length + 2; // quotes
            } else if (typeof value === 'number') {
                size += value.toString().length;
            } else if (Buffer.isBuffer(value)) {
                size += value.length * 2; // hex encoding
            } else {
                size += JSON.stringify(value).length;
            }

            if (i < Object.values(row).length - 1) {
                size += 1; // comma
            }
        }
        size += 1; // comma between rows
    }

    return size;
}

// Function to get optimal batch size based on data
function getOptimalBatchSize(tableName, columns, rows, maxPacketSize = 16 * 1024 * 1024) {
    if (rows.length === 0) return 1000;

    // Start with a small sample to estimate average row size
    const sampleSize = Math.min(10, rows.length);
    const sample = rows.slice(0, sampleSize);
    const samplePacketSize = estimatePacketSize(tableName, columns, sample);
    const avgRowSize = samplePacketSize / sampleSize;

    // Calculate safe batch size (use 50% of max packet size for safety margin)
    const safeMaxSize = maxPacketSize * 0.5;
    const optimalBatchSize = Math.floor(safeMaxSize / avgRowSize);

    // Ensure batch size is between 1 and 1000
    return Math.max(1, Math.min(1000, optimalBatchSize));
}

async function transferDatabase() {
    const remoteConfig = {
        host: process.env.REMOTE_DB_HOST,
        user: process.env.REMOTE_DB_USER,
        password: process.env.REMOTE_DB_PASSWORD,
        database: process.env.REMOTE_DB_NAME,
        port: process.env.REMOTE_DB_PORT,
        // Increase max_allowed_packet to handle large data
        maxAllowedPacket: 1024 * 1024 * 64, // 64MB
        acquireTimeout: 60000,
        timeout: 60000
    };

    const localConfig = {
        host: process.env.LOCAL_DB_HOST,
        user: process.env.LOCAL_DB_USER,
        password: process.env.LOCAL_DB_PASSWORD,
        database: process.env.LOCAL_DB_NAME,
        port: process.env.LOCAL_DB_PORT,
        // Increase max_allowed_packet to handle large data
        maxAllowedPacket: 1024 * 1024 * 64, // 64MB
        acquireTimeout: 60000,
        timeout: 60000
    };

    // Add confirmation prompt
    const confirmed = await askConfirmation(
        `Data will be copied from ${remoteConfig.host}::${remoteConfig.database} to ${localConfig.host}::${localConfig.database}. Are you sure you want to continue? y or n? `
    );

    if (!confirmed) {
        console.log('Transfer cancelled.');
        process.exit(0);
    }

    let remoteConnection, localConnection;

    try {
        // Create connections
        remoteConnection = await mysql.createConnection(remoteConfig);
        localConnection = await mysql.createConnection(localConfig);

        console.log('Connected to both databases successfully');

        // Set MySQL session variables for better handling of large packets
        await localConnection.query('SET SESSION max_allowed_packet = 67108864'); // 64MB
        await localConnection.query('SET SESSION net_buffer_length = 32768');
        await localConnection.query('SET SESSION bulk_insert_buffer_size = 268435456'); // 256MB

        // Disable foreign key checks
        await localConnection.query('SET FOREIGN_KEY_CHECKS = 0');

        // Get list of all tables from remote database
        const [remoteTables] = await remoteConnection.query('SHOW TABLES');
        const tableNames = remoteTables.map(row => row[`Tables_in_${remoteConfig.database}`]);

        console.log(`Found ${tableNames.length} tables to transfer`);

        for (const tableName of tableNames) {
            console.log(`\nProcessing table: ${tableName}`);

            // Get table structure from remote
            const [createTableRows] = await remoteConnection.query(`SHOW CREATE TABLE \`${tableName}\``);
            const createTableSql = createTableRows[0]['Create Table'];

            // Drop table if exists in local
            await localConnection.query(`DROP TABLE IF EXISTS \`${tableName}\``);
            console.log(`- Dropped existing table (if any)`);

            // Create table in local
            await localConnection.query(createTableSql);
            console.log(`- Created table structure`);

            if (['failed_jobs'].includes(tableName)) {
                continue;
            }

            // Get all data from remote table
            const [rows] = await remoteConnection.query(`SELECT * FROM \`${tableName}\``);

            if (rows.length === 0) {
                console.log(`- No data to insert`);
                continue;
            }

            // Escape all column names
            const columns = Object.keys(rows[0]).map(col => `\`${col}\``);

            // Get optimal batch size for this table
            const optimalBatchSize = getOptimalBatchSize(tableName, columns, rows);
            console.log(`- Using batch size: ${optimalBatchSize} (optimized for data size)`);

            // Insert data in batches with dynamic sizing
            for (let i = 0; i < rows.length; i += optimalBatchSize) {
                const batch = rows.slice(i, i + optimalBatchSize);

                try {
                    // Build placeholders and values
                    const valuesPlaceholders = batch.map(() => `(${columns.map(() => '?').join(',')})`).join(',');
                    const values = batch.flatMap(row => Object.values(row));

                    // Create the batch insert query with escaped identifiers
                    const batchInsertSql = `INSERT INTO \`${tableName}\` (${columns.join(',')}) VALUES ${valuesPlaceholders}`;

                    // Estimate packet size before executing
                    const estimatedSize = estimatePacketSize(tableName, columns, batch);
                    if (estimatedSize > 32 * 1024 * 1024) { // 32MB warning threshold
                        console.log(`  Warning: Large packet size estimated (${Math.round(estimatedSize / 1024 / 1024)}MB)`);
                    }

                    await localConnection.query(batchInsertSql, values);
                    console.log(`- Inserted ${Math.min(i + optimalBatchSize, rows.length)}/${rows.length} rows`);

                } catch (error) {
                    if (error.code === 'ER_NET_PACKET_TOO_LARGE') {
                        console.log(`  Packet too large error, retrying with smaller batches...`);

                        // Retry with much smaller batches (single rows if necessary)
                        const smallBatchSize = Math.max(1, Math.floor(optimalBatchSize / 10));
                        for (let j = 0; j < batch.length; j += smallBatchSize) {
                            const smallBatch = batch.slice(j, j + smallBatchSize);
                            const smallValuesPlaceholders = smallBatch.map(() => `(${columns.map(() => '?').join(',')})`).join(',');
                            const smallValues = smallBatch.flatMap(row => Object.values(row));
                            const smallBatchInsertSql = `INSERT INTO \`${tableName}\` (${columns.join(',')}) VALUES ${smallValuesPlaceholders}`;

                            await localConnection.query(smallBatchInsertSql, smallValues);
                            console.log(`  - Recovered: inserted ${j + smallBatchSize}/${batch.length} rows from failed batch`);
                        }
                    } else {
                        throw error; // Re-throw other errors
                    }
                }
            }
        }

        console.log('\nDatabase transfer completed successfully!');

    } catch (error) {
        console.error('Error during database transfer:', error);
    } finally {
        // Close connections
        if (remoteConnection) await remoteConnection.end();
        if (localConnection) await localConnection.end();
        console.log('Database connections closed');
    }
}

// Execute the transfer
transferDatabase();
