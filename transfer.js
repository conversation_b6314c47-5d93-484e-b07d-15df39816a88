const mysql = require('mysql2/promise');
const readline = require('readline');
require('dotenv').config();

async function askConfirmation(message) {
    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
    });

    return new Promise((resolve) => {
        rl.question(message, (answer) => {
            rl.close();
            resolve(answer.toLowerCase() === 'y');
        });
    });
}

// Function to estimate the size of a data packet in bytes
function estimatePacketSize(tableName, columns, batch) {
    let size = 0;

    // Base query overhead
    const baseQuery = `INSERT INTO \`${tableName}\` (${columns.join(',')}) VALUES `;
    size += baseQuery.length;

    // Estimate size for each row
    for (const row of batch) {
        size += 2; // parentheses
        for (let i = 0; i < Object.values(row).length; i++) {
            const value = Object.values(row)[i];
            if (value === null) {
                size += 4; // 'NULL'
            } else if (typeof value === 'string') {
                size += value.length + 2; // quotes
            } else if (typeof value === 'number') {
                size += value.toString().length;
            } else if (Buffer.isBuffer(value)) {
                size += value.length * 2; // hex encoding
            } else {
                size += JSON.stringify(value).length;
            }

            if (i < Object.values(row).length - 1) {
                size += 1; // comma
            }
        }
        size += 1; // comma between rows
    }

    return size;
}

// Function to get optimal batch size based on data
function getOptimalBatchSize(tableName, columns, rows, maxPacketSize = 512 * 1024) {
    if (rows.length === 0) return 10;

    // Start with a small sample to estimate average row size
    const sampleSize = Math.min(3, rows.length);
    const sample = rows.slice(0, sampleSize);
    const samplePacketSize = estimatePacketSize(tableName, columns, sample);
    const avgRowSize = samplePacketSize / sampleSize;

    // Calculate safe batch size (use 10% of max packet size for maximum safety)
    const safeMaxSize = maxPacketSize * 0.1;
    const optimalBatchSize = Math.floor(safeMaxSize / avgRowSize);

    // Ensure batch size is between 1 and 10 (very conservative for tables with large data)
    return Math.max(1, Math.min(10, optimalBatchSize));
}

async function transferDatabase() {
    const remoteConfig = {
        host: process.env.REMOTE_DB_HOST,
        user: process.env.REMOTE_DB_USER,
        password: process.env.REMOTE_DB_PASSWORD,
        database: process.env.REMOTE_DB_NAME,
        port: process.env.REMOTE_DB_PORT,
        // Crazy internal tool settings - go big or go home!
        connectTimeout: 300000, // 5 minutes
        acquireTimeout: 300000,
        timeout: 300000
    };

    const localConfig = {
        host: process.env.LOCAL_DB_HOST,
        user: process.env.LOCAL_DB_USER,
        password: process.env.LOCAL_DB_PASSWORD,
        database: process.env.LOCAL_DB_NAME,
        port: process.env.LOCAL_DB_PORT,
        // Crazy internal tool settings - go big or go home!
        connectTimeout: 300000, // 5 minutes
        acquireTimeout: 300000,
        timeout: 300000
    };

    // Add confirmation prompt
    const confirmed = await askConfirmation(
        `Data will be copied from ${remoteConfig.host}::${remoteConfig.database} to ${localConfig.host}::${localConfig.database}. Are you sure you want to continue? y or n? `
    );

    if (!confirmed) {
        console.log('Transfer cancelled.');
        process.exit(0);
    }

    let remoteConnection, localConnection;

    try {
        // Create connections
        remoteConnection = await mysql.createConnection(remoteConfig);
        localConnection = await mysql.createConnection(localConfig);

        console.log('Connected to both databases successfully');

        // 🚀 CRAZY INTERNAL TOOL MODE - MAXIMUM PACKET SIZES! 🚀
        console.log('🚀 Setting MAXIMUM packet sizes for internal tool...');

        // Try to set global variables (might need SUPER privilege)
        try {
            await localConnection.query('SET GLOBAL max_allowed_packet = 1073741824'); // 1GB
            await localConnection.query('SET GLOBAL net_buffer_length = 1073741824'); // 1GB
            await localConnection.query('SET GLOBAL bulk_insert_buffer_size = 1073741824'); // 1GB
            console.log('  ✅ Set GLOBAL packet settings to 1GB');
        } catch (globalError) {
            console.log('  ⚠️  Could not set GLOBAL settings (need SUPER privilege), trying session settings...');
        }

        // Set session variables (these should always work)
        try {
            await localConnection.query('SET SESSION sql_mode = ""'); // Disable strict mode
            await localConnection.query('SET SESSION innodb_lock_wait_timeout = 300'); // 5 minutes
            await localConnection.query('SET SESSION lock_wait_timeout = 300'); // 5 minutes
            await localConnection.query('SET SESSION wait_timeout = 28800'); // 8 hours
            await localConnection.query('SET SESSION interactive_timeout = 28800'); // 8 hours
            console.log('  ✅ Set session timeouts and modes for maximum compatibility');
        } catch (sessionError) {
            console.log('  ⚠️  Some session settings failed:', sessionError.message);
        }

        // Disable foreign key checks
        await localConnection.query('SET FOREIGN_KEY_CHECKS = 0');

        // Get list of all tables and views from remote database
        const [remoteTables] = await remoteConnection.query('SHOW FULL TABLES');

        // Separate tables and views
        const tables = [];
        const views = [];

        remoteTables.forEach(row => {
            const tableName = row[`Tables_in_${remoteConfig.database}`];
            const tableType = row['Table_type'];

            if (tableType === 'BASE TABLE') {
                tables.push(tableName);
            } else if (tableType === 'VIEW') {
                views.push(tableName);
            }
        });

        console.log(`Found ${tables.length} tables and ${views.length} views to process`);
        if (views.length > 0) {
            console.log(`Views (will be skipped): ${views.join(', ')}`);
        }

        for (const tableName of tables) {
            console.log(`\nProcessing table: ${tableName}`);

            // Get table structure from remote
            const [createTableRows] = await remoteConnection.query(`SHOW CREATE TABLE \`${tableName}\``);
            const createTableSql = createTableRows[0]['Create Table'];

            // Drop table if exists in local
            await localConnection.query(`DROP TABLE IF EXISTS \`${tableName}\``);
            console.log(`- Dropped existing table (if any)`);

            // Create table in local
            await localConnection.query(createTableSql);
            console.log(`- Created table structure`);

            if (['failed_jobs'].includes(tableName)) {
                continue;
            }

            // Get all data from remote table
            const [rows] = await remoteConnection.query(`SELECT * FROM \`${tableName}\``);

            if (rows.length === 0) {
                console.log(`- No data to insert`);
                continue;
            }

            // Escape all column names
            const columns = Object.keys(rows[0]).map(col => `\`${col}\``);

            // MAXIMUM AGGRESSION MODE - Use big batches! 🚀
            const aggressiveBatchSize = 10000; // Go big or go home!
            console.log(`- Using AGGRESSIVE batch size: ${aggressiveBatchSize} (internal tool mode)`);

            // Insert data in batches with MAXIMUM AGGRESSION
            for (let i = 0; i < rows.length; i += aggressiveBatchSize) {
                const batch = rows.slice(i, i + aggressiveBatchSize);

                try {
                    // Build placeholders and values
                    const valuesPlaceholders = batch.map(() => `(${columns.map(() => '?').join(',')})`).join(',');
                    const values = batch.flatMap(row => Object.values(row));

                    // Create the batch insert query with escaped identifiers
                    const batchInsertSql = `INSERT INTO \`${tableName}\` (${columns.join(',')}) VALUES ${valuesPlaceholders}`;

                    // Estimate packet size before executing
                    const estimatedSize = estimatePacketSize(tableName, columns, batch);
                    if (estimatedSize > 32 * 1024 * 1024) { // 32MB warning threshold
                        console.log(`  Warning: Large packet size estimated (${Math.round(estimatedSize / 1024 / 1024)}MB)`);
                    }

                    await localConnection.query(batchInsertSql, values);
                    console.log(`- Inserted ${Math.min(i + aggressiveBatchSize, rows.length)}/${rows.length} rows`);

                } catch (error) {
                    if (error.code === 'ER_NET_PACKET_TOO_LARGE' || error.code === 'EPIPE') {
                        console.log(`  ${error.code === 'EPIPE' ? 'Connection broken' : 'Packet too large'} error, reconnecting and retrying with smaller batches...`);

                        // Reconnect if connection was broken
                        if (error.code === 'EPIPE') {
                            try {
                                await localConnection.end();
                            } catch (e) {
                                // Ignore errors when closing broken connection
                            }
                            localConnection = await mysql.createConnection(localConfig);
                            await localConnection.query('SET FOREIGN_KEY_CHECKS = 0');
                            console.log(`  - Reconnected to local database`);
                        }

                        // Retry with much smaller batches (single rows if necessary)
                        const smallBatchSize = 1; // Use single row inserts for maximum safety
                        for (let j = 0; j < batch.length; j += smallBatchSize) {
                            const smallBatch = batch.slice(j, j + smallBatchSize);
                            const smallValuesPlaceholders = smallBatch.map(() => `(${columns.map(() => '?').join(',')})`).join(',');
                            const smallValues = smallBatch.flatMap(row => Object.values(row));
                            const smallBatchInsertSql = `INSERT INTO \`${tableName}\` (${columns.join(',')}) VALUES ${smallValuesPlaceholders}`;

                            try {
                                await localConnection.query(smallBatchInsertSql, smallValues);
                                console.log(`  - Recovered: inserted row ${i + j + 1}/${rows.length}`);
                            } catch (singleRowError) {
                                console.log(`  - Failed to insert row ${i + j + 1}, skipping: ${singleRowError.message}`);
                                // Continue with next row instead of failing completely
                            }
                        }
                    } else {
                        throw error; // Re-throw other errors
                    }
                }
            }
        }

        // Re-enable foreign key checks
        await localConnection.query('SET FOREIGN_KEY_CHECKS = 1');
        console.log('\nDatabase transfer completed successfully!');

    } catch (error) {
        console.error('Error during database transfer:', error);
    } finally {
        // Close connections
        if (remoteConnection) await remoteConnection.end();
        if (localConnection) await localConnection.end();
        console.log('Database connections closed');
    }
}

// Execute the transfer
transferDatabase();
