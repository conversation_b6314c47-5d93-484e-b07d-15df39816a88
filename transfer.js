const mysql = require('mysql2/promise');
const readline = require('readline');
require('dotenv').config();

async function askConfirmation(message) {
    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
    });

    return new Promise((resolve) => {
        rl.question(message, (answer) => {
            rl.close();
            resolve(answer.toLowerCase() === 'y');
        });
    });
}

async function transferDatabase() {
    const remoteConfig = {
        host: process.env.REMOTE_DB_HOST,
        user: process.env.REMOTE_DB_USER,
        password: process.env.REMOTE_DB_PASSWORD,
        database: process.env.REMOTE_DB_NAME,
        port: process.env.REMOTE_DB_PORT
    };

    const localConfig = {
        host: process.env.LOCAL_DB_HOST,
        user: process.env.LOCAL_DB_USER,
        password: process.env.LOCAL_DB_PASSWORD,
        database: process.env.LOCAL_DB_NAME,
        port: process.env.LOCAL_DB_PORT
    };

    // Add confirmation prompt
    const confirmed = await askConfirmation(
        `Data will be copied from ${remoteConfig.host}::${remoteConfig.database} to ${localConfig.host}::${localConfig.database}. Are you sure you want to continue? y or n? `
    );

    if (!confirmed) {
        console.log('Transfer cancelled.');
        process.exit(0);
    }

    let remoteConnection, localConnection;

    try {
        // Create connections
        remoteConnection = await mysql.createConnection(remoteConfig);
        localConnection = await mysql.createConnection(localConfig);

        console.log('Connected to both databases successfully');

        // Disable foreign key checks
        await localConnection.query('SET FOREIGN_KEY_CHECKS = 0');

        // Get list of all tables from remote database
        const [remoteTables] = await remoteConnection.query('SHOW TABLES');
        const tableNames = remoteTables.map(row => row[`Tables_in_${remoteConfig.database}`]);

        console.log(`Found ${tableNames.length} tables to transfer`);

        for (const tableName of tableNames) {
            console.log(`\nProcessing table: ${tableName}`);

            // Get table structure from remote
            const [createTableRows] = await remoteConnection.query(`SHOW CREATE TABLE \`${tableName}\``);
            const createTableSql = createTableRows[0]['Create Table'];

            // Drop table if exists in local
            await localConnection.query(`DROP TABLE IF EXISTS \`${tableName}\``);
            console.log(`- Dropped existing table (if any)`);

            // Create table in local
            await localConnection.query(createTableSql);
            console.log(`- Created table structure`);

            if (['failed_jobs'].includes(tableName)) {
                continue;
            }

            // Get all data from remote table
            const [rows] = await remoteConnection.query(`SELECT * FROM \`${tableName}\``);

            if (rows.length === 0) {
                console.log(`- No data to insert`);
                continue;
            }

            // Escape all column names
            const columns = Object.keys(rows[0]).map(col => `\`${col}\``);

            // Insert data in batches
            const batchSize = 1000;
            for (let i = 0; i < rows.length; i += batchSize) {
                const batch = rows.slice(i, i + batchSize);
                
                // Build placeholders and values
                const valuesPlaceholders = batch.map(() => `(${columns.map(() => '?').join(',')})`).join(',');
                const values = batch.flatMap(row => Object.values(row));
                
                // Create the batch insert query with escaped identifiers
                const batchInsertSql = `INSERT INTO \`${tableName}\` (${columns.join(',')}) VALUES ${valuesPlaceholders}`;
                
                await localConnection.query(batchInsertSql, values);
                console.log(`- Inserted ${Math.min(i + batchSize, rows.length)}/${rows.length} rows`);
            }
        }

        console.log('\nDatabase transfer completed successfully!');

    } catch (error) {
        console.error('Error during database transfer:', error);
    } finally {
        // Close connections
        if (remoteConnection) await remoteConnection.end();
        if (localConnection) await localConnection.end();
        console.log('Database connections closed');
    }
}

// Execute the transfer
transferDatabase();
